//this class will be used as backup for devices that do not support cloud storage
export class LocalStorageService {
  private storageKeyPrefix = 'gameData_'

  save(key: string, value: any): void {
    try {
      const jsonData = JSON.stringify(value)
      localStorage.setItem(this.storageKeyPrefix + key, jsonData)
    } catch (error) {
      console.error(`Error saving data to localStorage with key "${key}":`, error)
    }
  }

  load<T>(key: string): T | null {
    try {
      const jsonData = localStorage.getItem(this.storageKeyPrefix + key)
      return jsonData ? (JSON.parse(jsonData) as T) : null
    } catch (error) {
      console.error(`Error loading data from localStorage with key "${key}":`, error)
      return null
    }
  }

  delete(key: string): void {
    try {
      localStorage.removeItem(this.storageKeyPrefix + key)
    } catch (error) {
      console.error(`Error deleting data from localStorage with key "${key}":`, error)
    }
  }

  getKeys(): string[] {
    return Object.keys(localStorage).filter(key => key.startsWith(this.storageKeyPrefix))
  }
}

export const localStorageService = new LocalStorageService()

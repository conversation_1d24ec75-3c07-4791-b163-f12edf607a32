import { useUserStore } from '@/stores/userStore'
import { retrieveLaunchParams } from '@telegram-apps/sdk'
import * as Sentry from '@sentry/vue'

export function useUser() {
  const store = useUserStore()

  function setUser() {
    const { initData } = retrieveLaunchParams()
    store.setUser(initData?.user ?? null)

    Sentry.setUser({
      id: initData?.user?.id.toString() ?? 'unknown',
      username: initData?.user?.username ?? 'unknown',
      email: initData?.user?.username ? `${initData.user.username}@telegram.user` : undefined
    })
  }

  function getUser() {
    return store
  }

  return {
    setUser,
    getUser,
    user: store
  }
}

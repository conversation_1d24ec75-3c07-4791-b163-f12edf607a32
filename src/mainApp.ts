import './assets/main.scss'

import { VueQueryPlugin } from '@tanstack/vue-query'
import Particles from '@tsparticles/vue3'
import { loadFull } from 'tsparticles'
import { createApp } from 'vue'
import App from './App.vue'
import { i18n } from './plugins/i18n'
import { pinia } from './plugins/pinia'
import router from './router'

export function initApp() {

  if (__DEV__) {
    try {
      import('eruda').then(eruda => {
        eruda.default.init({
          useShadowDom: false,
          tool: ['console', 'elements', 'network', 'resources', 'info']
        })
        eruda.default.position({ x: 20, y: -200 })
      })
    } catch (e) {
      console.error(e)
    }
  }

  const app = createApp(App)
  app.use(i18n)
  app.use(pinia)
  app.use(router)
  app.use(VueQueryPlugin)
  app.use(Particles, {
    init: async engine => {
      await loadFull(engine)
    }
  })

  return app
}

import './assets/main.scss'

import { VueQueryPlugin } from '@tanstack/vue-query'
import Particles from '@tsparticles/vue3'
import { loadFull } from 'tsparticles'
import { createApp } from 'vue'
import App from './App.vue'
import { i18n } from './plugins/i18n'
import { pinia } from './plugins/pinia'
import router from './router'

import * as Sentry from '@sentry/vue'

export function initApp() {
  const app = createApp(App)

  Sentry.init({
    app,
    dsn: 'https://<EMAIL>/4509768256979024',
    environment: __ENV__,
    release: __VERSION__,
    sendDefaultPii: true,
    integrations: [Sentry.browserTracingIntegration({ router }), Sentry.replayIntegration()],
    tracesSampleRate: __DEV__ ? 1.0 : 0.1,
    tracePropagationTargets: [
      'localhost',
      /^https:\/\/unijump-.*\.k8s\.oriongames\.lol\/api/,
      /^https:\/\/.*\.oriongames\.lol\/api/
    ],
    replaysSessionSampleRate: 0.1,
    replaysOnErrorSampleRate: 1.0,
    enableLogs: true,
    beforeSend(event) {
      if (__DEV__) {
        console.log('Sentry event:', event)
      }
      return event
    }
  })

  if (__DEV__) {
    try {
      import('eruda').then(eruda => {
        eruda.default.init({
          useShadowDom: false,
          tool: ['console', 'elements', 'network', 'resources', 'info']
        })
        eruda.default.position({ x: 20, y: -200 })
      })
    } catch (e) {
      console.error(e)
    }
  }
  app.use(i18n)
  app.use(pinia)
  app.use(router)
  app.use(VueQueryPlugin)
  app.use(Particles, {
    init: async engine => {
      await loadFull(engine)
    }
  })

  return app
}
